<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
    <style>
    .extraction-summary {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .text-content {
        margin-top: 20px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        background-color: white;
        font-family: monospace;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    pre{
        white-space: break-spaces !important;
    }

    .spinner {
        display: none;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 20px auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .timer-display {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 5px;
        color: #1976d2;
        font-size: 16px;
    }

    .progress-log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin: 20px 0;
        max-height: 100px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        line-height: 1.4;
    }

    .progress-log .log-entry {
        margin-bottom: 5px;
        padding: 2px 0;
    }

    .progress-log .log-entry.info {
        color: #007bff;
    }

    .progress-log .log-entry.success {
        color: #28a745;
        font-weight: bold;
    }

    .progress-log .log-entry.error {
        color: #dc3545;
        font-weight: bold;
    }

    .progress-log .log-entry.warning {
        color: #ffc107;
    }

    .copy-button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .copy-button:hover {
        background-color: #0056b3;
    }

    .copy-button:active {
        background-color: #004085;
    }

    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 15px;
        border: 1px solid #c3e6cb;
        overflow-x: scroll;
    }

    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 15px;
        border: 1px solid #f5c6cb;
    }

    .view-text-button, .download-button {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .view-text-button:hover, .download-button:hover {
        background-color: #218838;
    }

    .view-text-button:disabled, .download-button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .view-text-button:disabled:hover, .download-button:disabled:hover {
        background-color: #cccccc;
    }

    .download-button {
        background-color: #2196F3;
    }

    .download-button:hover {
        background-color: #1976D2;
    }

    .buttons-container {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .link-container {
        margin-top: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #dee2e6;
    }

    .link-input {
        width: 70%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 10px;
        font-size: 14px;
    }

    .copy-link-button {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .copy-link-button:hover {
        background-color: #5a6268;
    }

    #forceReextract{
        margin-bottom: 0 !important;
    }
    </style>
    <title>MCQ Text Extractor</title>
</head>
<body>

    <div class="test-container">
        <div class="test-card">
            <h1>MCQ Text Extractor</h1>
            <p class="test-description">Enter a resource ID to extract text content from MCQ images</p>

            <form id="textExtractorForm" class="login-form">
                <label for="resId">Enter Res ID</label>
                <input type="text" id="resId" name="resId" required>

                <label for="totalQuestions">Total Number of Questions</label>
                <input type="number" id="totalQuestions" name="totalQuestions" min="1" required>

                <label for="explanationStartPage">Explanation Start Page Number</label>
                <input type="number" id="explanationStartPage" name="explanationStartPage" min="1" required>

                <div style="margin: 10px 0;">
                    <label style="display: flex; align-items: center; font-size: 14px;">
                        <input type="checkbox" id="forceReextract" name="forceReextract" style="margin-right: 8px;">
                        Force re-extraction (even if file already exists)
                    </label>
                </div>

                <button type="submit">Extract Text</button>
            </form>
            <div id="spinner" class="spinner"></div>
            <div id="timerDisplay" class="timer-display" style="display: none;">
                <strong>Elapsed Time: <span id="timerValue">00:00</span></strong>
            </div>
            <div id="progressLog" class="progress-log" style="display: none;"></div>

            <div id="resultsContainer" style="display: none; margin-top: 20px;">
                <h3>Extraction Results:</h3>
                <div id="extractionSummary" class="extraction-summary"></div>
                <div id="textContent" class="text-content"></div>
            </div>
        </div>
    </div>

<script src="/assets/bookGPTScripts/mcqExtractor.js"></script>
</body>
</html>
