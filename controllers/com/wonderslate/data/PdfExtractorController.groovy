package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.McqExtractLog
import com.wonderslate.log.McqTranslationLog
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.springframework.web.multipart.MultipartFile

import org.springframework.http.HttpStatus

class PdfExtractorController {
    PdfExtractorService pdfExtractorService
    DataProviderService dataProviderService
    def springSecurityService
    def redisService

    @Transactional
    def uploadPdfImages() {
        try {
            boolean isQuizImages = false
            boolean isExtractedImages = false
            def bookId = params.long('bookId')
            def chapterId = params.long('chapterId')
            def resId = params.long('resId')
            def quizImages = params.('quizImages')
            def extractedImages = params.('extractedImages')

            if(quizImages){
                isQuizImages = true;
            }

            if(extractedImages){
                isExtractedImages = true
            }
            // Cast request to MultipartHttpServletRequest
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request
            def imageFiles = multiRequest.getFiles("images")
            def croppedImages = multiRequest.getFiles("cropped_images")

            if (!bookId || imageFiles.isEmpty()) {
                render([status: 400, message: "BookId and image files are required"] as JSON)
                return
            }

            def result = pdfExtractorService.processPdfImages(bookId, chapterId, resId, imageFiles, isQuizImages, isExtractedImages, croppedImages)
            if (result.success) {
                render([status: 200, message: "Images uploaded successfully", data: result.data] as JSON)
            } else {
                render([status: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in uploadPdfImages: ${e.message}", e)
            render([status: 500, message: "Internal server error"] as JSON)
        }
    }

    @Transactional
    def getImageLinks() {
        try {
            def bookId = params.long('bookId')
            def chapterId = params.long('chapterId')
            def resId = params.long('resId')
            def quizImages = params.boolean('quizImages')

            if (!bookId) {
                render([status: 400, message: "BookId is required"] as JSON)
                return
            }

            def result = pdfExtractorService.getImagePaths(bookId, chapterId, resId, quizImages, false)
            render([status: result.success ? 200 : 500, data: result.data, message: result.message] as JSON)

        } catch (Exception e) {
            log.error("Error getting image links: ${e.message}", e)
            render([status: 500, message: e.message] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def taskHandler(){
        try {
            def requestBody = request.JSON
            String apiEndpoint = "extract"
            def result = pdfExtractorService.fetchQuestions(request, apiEndpoint, requestBody)
            render([
                    status: result.success ? 200 : 500,
                    data: result.data,
                    message: result.message
            ] as JSON)
        } catch (Exception e) {
            log.error("Error extracting questions: ${e.message}", e)
            render([status: 500, message: e.message] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqExtractor() {
        if ("running".equals("" + redisService.("mcqExtractor_" + params.chapterId))) {
            return [status: 200, error: true, message: "Extraction is already in progress for this chapter.", resLink: null]
        } else {
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(params.chapterId))
            if (chaptersMst == null) {
                return [status: 200, error: true, message: "Chapter details not found.", resLink: null]
            }
            if(chaptersMst.mcqsExtracted!=null&&"true".equals(chaptersMst.mcqsExtracted)){
                return [status: 200, error: true, message: "MCQs are already extracted for this chapter.", resLink: null]
            }
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes", [sort: "id", order: "asc"])
            if (readingMaterials.size() > 0) {
                redisService.("mcqExtractor_" + params.chapterId) = "running"
                params.put("resId", "" + readingMaterials[0].id)
                return [status     : 200, error: false, message: "",
                 resLink    : readingMaterials[0].resLink, bookTitle: booksMst.title,
                 chapterName: chaptersMst.name, chapterId: chaptersMst.id,
                 resId      : readingMaterials[0].id, resName: readingMaterials[0].resourceName
                ]
            } else {
                return [status: 200, error: true, message: "No PDF found for this chapter", resLink: null]
            }
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def startMcqExtraction(){
        try {
            /*
            External API: /mcq-text-extractor
            Sample request body:
            {
                "resId": "39100",
                "force_reextract": false,
                "total_questions": 40,
                "explanation_start_page": 3
            }

            Sample response:
           {
                "status": "started",
                "task_id": "1f4984d0-d48f-4315-a53b-1771b83d540f",
                "mcq_task_id": "0cffc31c-20d0-4ade-88a4-6c3e5b70e10d",
                "message": "MCQ text extraction started in background",
                "response_time": "0.041s"
            }
             */
        } catch (Exception e) {
            log.error("Error in startMcqExtraction: ${e.message}", e)
            render([status: 500, message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def downloadMcqTextFile() {
        try {
            /*
            External API: /download-mcq-text/${result.chapter_id}/${result.resource_id}
            This will download the text file from S3
            so need to download and then again send it to client to download as how this api returns the response
             */
        } catch (Exception e) {
            log.error("Error in downloadMcqTextFile: ${e.message}", e)
            render([status: 500, message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqExtractorStatus() {
        try {
            /*
            External API: /mcq-text-extractor/status
            Sample request body:
            {
                "task_id": "1f4984d0-d48f-4315-a53b-1771b83d540f"
            }

            Sample response 1:
           {
                "status": "IN_PROGRESS",
                "task_id": "1f4984d0-d48f-4315-a53b-1771b83d540f",
                "created_at": "2025-06-12T08:07:48",
                "last_updated": "2025-06-12T08:07:48",
                "error": false,
                "error_message": null
            }

            Sample response 2: when completed
            {
                "status": "COMPLETED",
                "task_id": "1f4984d0-d48f-4315-a53b-1771b83d540f",
                "message": "MCQ text extraction completed successfully",
                "completed_at": "2025-06-12T08:14:00",
                "result": {
                    "status": "success",
                    "message": "Successfully extracted text from 13 images",
                    "s3_path": "supload/pdfextracts/312749/22516/39100/extractedImages/22516_39100.txt",
                    "total_images": 13,
                    "text_files_created": 13,
                    "chapter_id": 22516,
                    "resource_id": 39100,
                    "mcq_parsing": {
                        "total_questions": 40,
                        "json_s3_path": "supload/pdfextracts/312749/22516/39100/extractedImages/22516_39100_mcqs.json",
                        "parsing_completed": true,
                        "api_processing_completed": true
                    }
                }
            }
             */
        } catch (Exception e) {
            log.error("Error in mcqExtractorStatus: ${e.message}", e)
            render([status: 500, message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def getMCQText() {
        try {
            /*
            External API: /get-mcq-text/${chapterId}/${resId}
            Method: GET

            Sample response:
           {
                "status": "success",
                "content": "1. If \\(a : b = 4 : 5\\), \\(b : c = 2 : 3\\) ",
                "source": "s3_direct",
                "s3_path": "/s3b/qa/supload/pdfextracts/312749/22516/39100/extractedImages/22516_39100.txt"
            }
             */
        } catch (Exception e) {
            log.error("Error in getMCQText: ${e.message}", e)
            render([status: 500, message: "Internal server error"] as JSON)
        }
    }


    @Secured(['ROLE_PDF_EXTRACTOR'])
    def createSolution(){

    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def createMcqSolution(){
        try {
            def multipartFiles = []

            // Get image order information if provided
            def sampleImageOrder = params.sample_image_order ? new groovy.json.JsonSlurper().parseText(params.sample_image_order) : null
            def questionImageOrder = params.question_image_order ? new groovy.json.JsonSlurper().parseText(params.question_image_order) : null

            // Handle sample images with ordering
            def sampleImageFiles = []
            request.multiFileMap.findAll { key, value ->
                key.startsWith('sample_image_')
            }.each { key, files ->
                files.each { file ->
                    // Extract the index from the key (sample_image_X)
                    def index = key.substring('sample_image_'.length()) as Integer
                    sampleImageFiles << [index: index, name: key, file: file]
                }
            }

            // Sort sample images if order is provided
            if (sampleImageOrder) {
                // Reorder the files based on the provided order
                def orderedSampleFiles = []
                sampleImageOrder.eachWithIndex { orderIndex, newIndex ->
                    def fileEntry = sampleImageFiles.find { it.index == orderIndex }
                    if (fileEntry) {
                        // Update the name to reflect the new order
                        fileEntry.name = "sample_image_${newIndex}"
                        orderedSampleFiles << fileEntry
                    }
                }
                // Add ordered files to multipartFiles
                orderedSampleFiles.each { multipartFiles << [name: it.name, file: it.file] }
            } else {
                // No ordering, just add files as they are
                sampleImageFiles.each { multipartFiles << [name: it.name, file: it.file] }
            }

            // Handle question images with ordering
            def questionImageFiles = []
            request.multiFileMap.findAll { key, value ->
                key.startsWith('question_image_')
            }.each { key, files ->
                files.each { file ->
                    // Extract the index from the key (question_image_X)
                    def index = key.substring('question_image_'.length()) as Integer
                    questionImageFiles << [index: index, name: key, file: file]
                }
            }

            // Sort question images if order is provided
            if (questionImageOrder) {
                // Reorder the files based on the provided order
                def orderedQuestionFiles = []
                questionImageOrder.eachWithIndex { orderIndex, newIndex ->
                    def fileEntry = questionImageFiles.find { it.index == orderIndex }
                    if (fileEntry) {
                        // Update the name to reflect the new order
                        fileEntry.name = "question_image_${newIndex}"
                        orderedQuestionFiles << fileEntry
                    }
                }
                // Add ordered files to multipartFiles
                orderedQuestionFiles.each { multipartFiles << [name: it.name, file: it.file] }
            } else {
                // No ordering, just add files as they are
                questionImageFiles.each { multipartFiles << [name: it.name, file: it.file] }
            }

            // Get text inputs
            def sampleText = params.sample_text
            def questionText = params.question_text

            // Call service to create solution
            def result = pdfExtractorService.createMcqSolution(sampleText, questionText, multipartFiles, request)

            render([status: result.success ? 200 : 500, solution: result.solution, ] as JSON)
        } catch (Exception e) {
            log.error("Error processing solution request", e)
            render([success: false, error: e.message] as JSON)
        }
    }

    @Transactional
    def mcqsExtracted(){
        try {
            redisService.("mcqExtractor_"+params.chapterId)=null
            def chaptersMst = ChaptersMst.findById(new Long(params.chapterId))
            chaptersMst.mcqsExtracted="true"
            chaptersMst.save(failOnError: true, flush: true)
            def json = [response:"success"]
            render json as JSON
        }catch (Exception e) {
            log.error("Error processing solution request", e)
            render([response:"failed",success: false, error: e.message] as JSON)
        }

    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def extractAndValidateMcq() {
        try {
            def requestBody = request.JSON
            def resId = requestBody?.resId ?: params.resId

            if (!resId) {
                render([status: 400, message: "Resource ID is required"] as JSON)
                return
            }

            // Extract additional parameters from request body
            def totalQuestions = requestBody?.total_questions
            def explanationStartPage = requestBody?.explanation_start_page
            def forceReextract = requestBody?.force_reextract ?: false

            def result = pdfExtractorService.extractAndValidateMcq(request, resId, session, totalQuestions, explanationStartPage, forceReextract)

            if (result.success) {
                render([status: 200, data: result.data] as JSON)
            } else {
                render([status: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in extractAndValidateMcq: ${e.message}", e)
            render([status: 500, message: "Internal server error: ${e.message}"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqTranslator(){
        // Render the MCQ translator page
        [title: "MCQ Translator"]
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqTranslatorFile() {
        try {
            // Cast request to MultipartHttpServletRequest
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request
            MultipartFile pdfFile = multiRequest.getFile("pdfFile")

            if (!pdfFile || pdfFile.isEmpty()) {
                render([status: 'error', message: "PDF file is required"] as JSON)
                return
            }

            def totalQuestions = params.total_questions
            def sourceLanguage = params.source_language
            def destinationLanguage = params.destination_language
            def username = params.username ?: springSecurityService.currentUser?.username

            // Validate required parameters
            if (!totalQuestions || !sourceLanguage || !destinationLanguage) {
                render([status: 'error', message: "All fields are required"] as JSON)
                return
            }

            if (sourceLanguage == destinationLanguage) {
                render([status: 'error', message: "Source and destination languages must be different"] as JSON)
                return
            }

            // Call service to handle the translation
            def result = pdfExtractorService.translateMcqFile(
                pdfFile,
                totalQuestions as Integer,
                sourceLanguage,
                destinationLanguage,
                username,
                request
            )

            if (result.success) {
                render([
                    status: 'success',
                    translation_id: result.translationId,
                    original_s3_path: result.originalS3Path,
                    translated_s3_path: result.translatedS3Path,
                    message: result.message
                ] as JSON)
            } else {
                render([status: 'error', message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in mcqTranslatorFile: ${e.message}", e)
            render([status: 'error', message: "Internal server error: ${e.message}"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def getTranslatedMcqContent() {
        try {
            def translationId = params.id

            if (!translationId) {
                render([status: 'error', message: "Translation ID is required"] as JSON)
                return
            }

            def result = pdfExtractorService.getTranslatedContent(translationId, request)

            if (result.success) {
                render([
                    status: 'success',
                    content: result.content
                ] as JSON)
            } else {
                render([status: 'error', message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in getTranslatedMcqContent: ${e.message}", e)
            render([status: 'error', message: "Internal server error: ${e.message}"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def downloadTranslatedMcq() {
        try {
            def translationId = params.id

            if (!translationId) {
                render([status: 'error', message: "Translation ID is required"] as JSON)
                return
            }

            def result = pdfExtractorService.downloadTranslatedFile(translationId, request, response)

            if (!result.success) {
                render([status: 'error', message: result.message] as JSON)
            }
            // If successful, the service method handles the file download response
        } catch (Exception e) {
            log.error("Error in downloadTranslatedMcq: ${e.message}", e)
            render([status: 'error', message: "Internal server error: ${e.message}"] as JSON)
        }
    }

}
